import pandas as pd
import os
import sys

# Setup project root path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from db_writer import DBWriter
from utils import (
    load_retail_data, prepare_location_data, classify_subclasses_into_quadrants,
    calculate_and_apply_bounds, optimize_lm_space, adjust_to_match_space_by_cover,
    calculate_gmv_metrics, save_results
)
from shared_config import get_config


class OptimizationProcessor:
    def __init__(self):
        self.cfg = get_config()
        self.data_new, self.data_sat = load_retail_data()
        self.optimization_metric = self.cfg.get("OPTIMIZATION_ON", "GMV").upper()

    def run(self):
        print(f"Loaded data: Performance shape {self.data_new.shape}, Saturation shape {self.data_sat.shape}")
        print("Optimization is on 2nd print:", self.optimization_metric)

        for loc_cd in self.cfg['locations']:
            print(f"\n=== Processing location {loc_cd} ===")
            self.process_location(loc_cd)

    def process_location(self, loc_cd):
        valid_df, invalid_df, df_loc = prepare_location_data(
            self.data_new, self.data_sat, loc_cd=loc_cd, include_invalid=False
        )
        print(f"Valid subclasses: {valid_df.shape[0]}, Invalid subclasses: {invalid_df.shape[0]}")

        # cover_file_path = self.cfg.get("COVER_FILE")
        # cover_cutoff_df = pd.read_excel(cover_file_path)
        cover_cutoff_table = self.cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["COVER_DEPTH_DATA"]
        writer = DBWriter(self.cfg)
        cover_cutoff_df = writer.read(        
        table_name=cover_cutoff_table,
        # latest_by="created_at",
        latest_only=True)

        # Clean and filter cover cutoffs
        UPPER_COLS = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM']
        cover_cutoff_df[UPPER_COLS] = cover_cutoff_df[UPPER_COLS].apply(lambda col: col.str.upper())
        cover_cutoff_df['LOC_CD'] = cover_cutoff_df['LOC_CD'].astype(str).str.strip()
        cover_cutoff_df['SUB_CLSS_NM'] = cover_cutoff_df['SUB_CLSS_NM'].str.strip()
        loc_cd = str(loc_cd).strip()
        cover_cutoff_filtered = cover_cutoff_df[cover_cutoff_df['LOC_CD'] == loc_cd]

        if cover_cutoff_filtered.empty:
            print(f"No cover cutoffs found for LOC_CD = {loc_cd}, default thresholds will be used.")
        else:
            print(f"Using {len(cover_cutoff_filtered)} cover cutoffs for LOC_CD = {loc_cd}")

        cover_cutoffs = cover_cutoff_filtered.set_index("SUB_CLSS_NM")[
            ["MID_COVER_START", "HIGH_COVER_START"]
        ].to_dict(orient="index")

        # debug_cover_path = f"C:/Users/<USER>/OneDrive - Landmark Group india/Desktop/Space_Optimization/Option_rationalization/DE_Final_set_code_04_08_v2/data/output/cover_thresholds_debug_loc_{loc_cd}.xlsx"
        # pd.DataFrame.from_dict(cover_cutoffs, orient="index").to_excel(debug_cover_path)

        df2 = classify_subclasses_into_quadrants(valid_df, cover_cutoffs)

        valid_df1, invalid_df1 = calculate_and_apply_bounds(df2, loc_cd)
        print(f"Bounds calculated: Valid {valid_df1.shape[0]}, Invalid {invalid_df1.shape[0]}")

        final_invalid_df = pd.concat([invalid_df, invalid_df1], ignore_index=True)
        writer = DBWriter(self.cfg)
        writer.write(final_invalid_df, "INVALID_DATA_CNSTRNT_VIL")

        result_df = optimize_lm_space(
            valid_df1,
            cfg=self.cfg['DEFAULT_OPTIMIZATION_CONFIG'],
            override_dict=self.cfg['OVERRIDE_DICT'],
            optimization_metric=self.optimization_metric
        )

        if result_df is None:
            print(f"Optimization failed for location {loc_cd}")
            return

        original_space = result_df[result_df['action'].isin(['increase', 'decrease'])]['current_lm'].sum()
        adjusted_df = adjust_to_match_space_by_cover(
            result_df,
            original_space,
            cfg=self.cfg['DEFAULT_OPTIMIZATION_CONFIG']
        )

        adjusted_df = calculate_gmv_metrics(adjusted_df, self.data_new)
        save_results(adjusted_df, loc_cd)


if __name__ == "__main__":
    processor = OptimizationProcessor()
    processor.run()
